#!/usr/bin/env python3
"""
预留地址映射生成类 (Reserved Address Mapping Generator)
实现可配置的多配置空间地址映射生成功能
"""

import yaml
import random
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass


@dataclass
class SubField:
    """子字段数据类"""
    name: str                    # 子字段名
    start_bit: int              # 起始bit
    end_bit: int                # 结束bit
    default_offset: int         # 默认偏移大小
    valid_ranges: List[List[int]]  # 合法的配置范围
    disable_enable: bool        # 失能功能开关
    disable_value: Optional[int] # 失能值
    mapping_field: str          # 映射的mapping字段名
    base_value: int             # 默认基值大小
    
    @property
    def bits(self) -> int:
        """字段位宽"""
        return self.end_bit - self.start_bit + 1
    
    @property
    def max_value(self) -> int:
        """字段最大值"""
        return (1 << self.bits) - 1
    
    def is_valid_value(self, value: int) -> bool:
        """检查值是否在任一有效范围内"""
        return any(min_val <= value <= max_val for min_val, max_val in self.valid_ranges)
    
    def get_actual_value(self, config_value: int) -> int:
        """获取实际值（配置值+基值）"""
        if self.disable_enable and config_value == self.get_disable_value():
            return config_value  # 失能状态返回失能值
        return config_value + self.base_value
    
    def get_disable_value(self) -> int:
        """获取失能值"""
        return self.disable_value if self.disable_value is not None else self.max_value


@dataclass
class ConfigSpace:
    """配置空间数据类"""
    name: str                   # 配置空间名称
    bit_width: int             # 数据位宽 (16, 32, 64)
    sub_fields: Dict[str, SubField]  # 子字段字典
    
    def get_total_bits(self) -> int:
        """获取总位数"""
        return self.bit_width


class ReservedAddrMapGenerator:
    """预留地址映射生成类"""
    
    def __init__(self, config_file: str = "reserved_config.yaml"):
        """
        初始化预留地址映射生成器
        
        Args:
            config_file: YAML配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.config_spaces = self._parse_config_spaces()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _parse_config_spaces(self) -> Dict[str, ConfigSpace]:
        """解析配置空间"""
        spaces = {}
        
        for space_name, space_config in self.config['config_spaces'].items():
            # 解析子字段
            sub_fields = {}
            for field_name, field_config in space_config['sub_fields'].items():
                sub_fields[field_name] = SubField(
                    name=field_name,
                    start_bit=field_config['start_bit'],
                    end_bit=field_config['end_bit'],
                    default_offset=field_config.get('default_offset', 0),
                    valid_ranges=field_config.get('valid_ranges', [[0, (1 << (field_config['end_bit'] - field_config['start_bit'] + 1)) - 1]]),
                    disable_enable=field_config.get('disable_enable', False),
                    disable_value=field_config.get('disable_value'),
                    mapping_field=field_config['mapping_field'],
                    base_value=field_config.get('base_value', 0)
                )
            
            spaces[space_name] = ConfigSpace(
                name=space_name,
                bit_width=space_config['bit_width'],
                sub_fields=sub_fields
            )
        
        return spaces
    
    def print_field_description(self):
        """打印字段描述功能"""
        print("=" * 100)
        print("预留地址映射配置空间描述 (Reserved Address Mapping Configuration)")
        print("=" * 100)
        
        for space_name, space in self.config_spaces.items():
            print(f"\n配置空间: {space_name}")
            print(f"  位宽: {space.bit_width} 位")
            print(f"  子字段数量: {len(space.sub_fields)}")
            print("-" * 80)
            
            for field_name, field in space.sub_fields.items():
                ranges_str = ', '.join(f"[{r[0]}, {r[1]}]" for r in field.valid_ranges)
                disable_info = ""
                if field.disable_enable:
                    disable_info = f", 失能值: {field.get_disable_value()}"
                
                print(f"  {field_name:12s}: 位[{field.end_bit:2d}:{field.start_bit:2d}] "
                      f"({field.bits:2d}位) 映射到: {field.mapping_field:8s}")
                print(f"                有效范围: {ranges_str}")
                print(f"                基值: {field.base_value:4d}, 偏移: {field.default_offset:4d}{disable_info}")
                print()
        
        print("=" * 100)
    
    def generate_random_config(self, fixed_configs: Optional[Dict[str, Dict[str, int]]] = None) -> Dict[str, Dict[str, int]]:
        """
        随机生成配置
        
        Args:
            fixed_configs: 固定配置 {空间名: {字段名: 值}}
            
        Returns:
            生成的配置 {空间名: {字段名: 配置值}}
        """
        if fixed_configs is None:
            fixed_configs = {}
        
        result = {}
        
        for space_name, space in self.config_spaces.items():
            space_config = {}
            fixed_space_config = fixed_configs.get(space_name, {})
            
            for field_name, field in space.sub_fields.items():
                if field_name in fixed_space_config:
                    # 使用固定配置
                    config_value = fixed_space_config[field_name]
                    if not field.is_valid_value(config_value):
                        ranges_str = ', '.join(f"[{r[0]}, {r[1]}]" for r in field.valid_ranges)
                        raise ValueError(f"固定配置中 {space_name}.{field_name} 的值 {config_value} 不在有效范围内: {ranges_str}")
                    space_config[field_name] = config_value
                else:
                    # 随机生成
                    if field.disable_enable and random.random() < 0.1:  # 10%概率生成失能值
                        space_config[field_name] = field.get_disable_value()
                    else:
                        # 随机选择一个有效范围
                        valid_range = random.choice(field.valid_ranges)
                        min_val, max_val = valid_range
                        space_config[field_name] = random.randint(min_val, max_val)
            
            result[space_name] = space_config
        
        return result
    
    def print_specific_config(self, config: Dict[str, Dict[str, int]]):
        """
        打印具体配置
        
        Args:
            config: 配置字典 {空间名: {字段名: 配置值}}
        """
        print("=" * 100)
        print("具体配置信息 (Specific Configuration)")
        print("=" * 100)
        
        for space_name, space_config in config.items():
            if space_name not in self.config_spaces:
                print(f"警告: 未知的配置空间 {space_name}")
                continue
                
            space = self.config_spaces[space_name]
            print(f"\n配置空间: {space_name} ({space.bit_width}位)")
            print("-" * 80)
            
            # 构造完整的配置值
            full_value = 0
            mapping_result = {}
            
            for field_name, config_value in space_config.items():
                if field_name not in space.sub_fields:
                    print(f"  警告: 未知的字段 {field_name}")
                    continue
                
                field = space.sub_fields[field_name]
                actual_value = field.get_actual_value(config_value)
                
                # 设置位值
                mask = ((1 << field.bits) - 1) << field.start_bit
                full_value = (full_value & ~mask) | ((config_value & ((1 << field.bits) - 1)) << field.start_bit)
                
                # 记录映射结果
                mapping_result[field.mapping_field] = actual_value
                
                # 打印字段信息
                status = "失能" if (field.disable_enable and config_value == field.get_disable_value()) else "启用"
                print(f"  {field_name:12s}: 配置值={config_value:4d} 实际值={actual_value:4d} "
                      f"映射到={field.mapping_field:8s} 状态={status}")
            
            print(f"\n  完整配置值: 0x{full_value:0{space.bit_width//4}x} ({full_value:0{space.bit_width}b})")
            print(f"  映射结果: {mapping_result}")
            print()
        
        print("=" * 100)
    
    def generate_mapping_table(self, config: Dict[str, Dict[str, int]]) -> Dict[str, int]:
        """
        生成映射表
        
        Args:
            config: 配置字典 {空间名: {字段名: 配置值}}
            
        Returns:
            映射表 {映射字段名: 实际值}
        """
        mapping_table = {}
        
        for space_name, space_config in config.items():
            if space_name not in self.config_spaces:
                continue
                
            space = self.config_spaces[space_name]
            
            for field_name, config_value in space_config.items():
                if field_name not in space.sub_fields:
                    continue
                
                field = space.sub_fields[field_name]
                actual_value = field.get_actual_value(config_value)
                mapping_table[field.mapping_field] = actual_value
        
        return mapping_table
