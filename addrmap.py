#!/usr/bin/env python3
"""
地址映射系统 (Address Mapping System)
实现地址到内部映射字段的转换功能
"""

import yaml
import random
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass


@dataclass
class MappingField:
    """映射字段数据类"""
    name: str
    bits: int
    position: List[int]  # [高位, 低位]
    valid_ranges: List[List[int]]  # 多个有效范围 [[min1, max1], [min2, max2], ...]
    description: str = ""

    @property
    def valid_range(self) -> List[int]:
        """兼容性属性，返回第一个有效范围"""
        return self.valid_ranges[0] if self.valid_ranges else [0, 0]

    def is_valid_value(self, value: int) -> bool:
        """检查值是否在任一有效范围内"""
        return any(min_val <= value <= max_val for min_val, max_val in self.valid_ranges)


class AddrMap:
    """地址映射核心类"""
    
    def __init__(self, config_file: str = "config.yaml"):
        """
        初始化地址映射器
        
        Args:
            config_file: YAML配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.mapping_fields = self._parse_mapping_fields()
        self.address_space = self.config['address_space']
        
    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _parse_mapping_fields(self) -> Dict[str, MappingField]:
        """解析映射字段配置"""
        fields = {}
        for name, config in self.config['mapping_fields'].items():
            # 支持多种有效范围格式
            valid_ranges = config.get('valid_ranges', [])
            if not valid_ranges:
                # 兼容旧格式 valid_range
                if 'valid_range' in config:
                    valid_ranges = [config['valid_range']]
                else:
                    # 默认范围为整个位宽
                    max_val = (1 << config['bits']) - 1
                    valid_ranges = [[0, max_val]]

            fields[name] = MappingField(
                name=name,
                bits=config['bits'],
                position=config['position'],
                valid_ranges=valid_ranges,
                description=config.get('description', '')
            )
        return fields
    
    def check_addrmap(self) -> Dict[str, Any]:
        """
        检查映射表是否完整（唯一对应，覆盖地址大小）
        
        Returns:
            检查结果字典，包含是否通过和详细信息
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'coverage': {},
            'overlap_check': True
        }
        
        # 检查位重叠
        if self.config['validation']['check_overlap']:
            overlap_errors = self._check_bit_overlap()
            if overlap_errors:
                result['valid'] = False
                result['errors'].extend(overlap_errors)
                result['overlap_check'] = False
        
        # 检查覆盖完整性
        if self.config['validation']['check_coverage']:
            coverage_info = self._check_coverage()
            result['coverage'] = coverage_info
            if not coverage_info['complete']:
                result['warnings'].append(f"地址覆盖不完整，未覆盖位: {coverage_info['uncovered_bits']}")
        
        # 检查有效范围
        if self.config['validation']['check_range']:
            range_errors = self._check_valid_ranges()
            if range_errors:
                result['errors'].extend(range_errors)
                result['valid'] = False
        
        return result
    
    def _check_bit_overlap(self) -> List[str]:
        """检查位重叠"""
        errors = []
        used_bits = set()
        
        for field_name, field in self.mapping_fields.items():
            high_bit, low_bit = field.position
            field_bits = set(range(low_bit, high_bit + 1))
            
            overlap = used_bits & field_bits
            if overlap:
                errors.append(f"字段 {field_name} 与其他字段在位 {sorted(overlap)} 上重叠")
            
            used_bits.update(field_bits)
        
        return errors
    
    def _check_coverage(self) -> Dict[str, Any]:
        """检查地址覆盖完整性"""
        total_bits = self.address_space['total_bits']
        covered_bits = set()
        
        for field in self.mapping_fields.values():
            high_bit, low_bit = field.position
            covered_bits.update(range(low_bit, high_bit + 1))
        
        all_bits = set(range(total_bits))
        uncovered_bits = all_bits - covered_bits
        
        return {
            'complete': len(uncovered_bits) == 0,
            'covered_bits': sorted(covered_bits),
            'uncovered_bits': sorted(uncovered_bits),
            'coverage_ratio': len(covered_bits) / total_bits
        }
    
    def _check_valid_ranges(self) -> List[str]:
        """检查有效范围"""
        errors = []

        for field_name, field in self.mapping_fields.items():
            max_possible = (1 << field.bits) - 1

            for i, (min_range, max_range) in enumerate(field.valid_ranges):
                if max_range > max_possible:
                    errors.append(f"字段 {field_name} 的有效范围 {i+1} 最大值 {max_range} 超过了 {field.bits} 位的最大值 {max_possible}")

                if min_range < 0:
                    errors.append(f"字段 {field_name} 的有效范围 {i+1} 最小值 {min_range} 不能为负数")

                if min_range > max_range:
                    errors.append(f"字段 {field_name} 的有效范围 {i+1} 最小值 {min_range} 大于最大值 {max_range}")

        return errors

    def addr_to_inner_addr(self, address: int) -> int:
        """
        将地址转换成内部地址

        Args:
            address: 输入地址

        Returns:
            内部地址
        """
        # 减去基址偏移
        base_addr = self.address_space.get('base_address', 0)
        inner_addr = address - base_addr

        # 应用内部地址偏移
        offset = self.config['address_conversion'].get('inner_addr_offset', 0)
        inner_addr += offset

        # 检查对齐
        alignment = self.config['address_conversion'].get('alignment', 1)
        if inner_addr % alignment != 0:
            raise ValueError(f"地址 0x{address:08x} 不满足 {alignment} 字节对齐要求")

        return inner_addr

    def addr_to_mapping(self, address: int) -> Dict[str, int]:
        """
        地址转换成内部地址，再转换成具体的映射表信息

        Args:
            address: 输入地址

        Returns:
            映射字段字典 {字段名: 值}
        """
        # 转换为内部地址
        inner_addr = self.addr_to_inner_addr(address)

        # 提取各字段值
        mapping = {}
        for field_name, field in self.mapping_fields.items():
            high_bit, low_bit = field.position

            # 提取位段
            mask = ((1 << (high_bit - low_bit + 1)) - 1) << low_bit
            value = (inner_addr & mask) >> low_bit

            # 检查有效范围
            if self.config['validation']['check_range']:
                if not field.is_valid_value(value):
                    ranges_str = ', '.join(f"[{r[0]}, {r[1]}]" for r in field.valid_ranges)
                    raise ValueError(f"字段 {field_name} 的值 {value} 不在任何有效范围内: {ranges_str}")

            mapping[field_name] = value

        return mapping

    def print_addrmap(self, address: Optional[int] = None):
        """
        打印映射信息，将完整地址形式，打印每1bit将映射的字段名

        Args:
            address: 可选的具体地址，如果提供则同时显示该地址的映射值
        """
        total_bits = self.address_space['total_bits']

        print("=" * 80)
        print("地址映射表 (Address Mapping Table)")
        print("=" * 80)

        # 创建位到字段的映射
        bit_to_field = {}
        for field_name, field in self.mapping_fields.items():
            high_bit, low_bit = field.position
            for bit in range(low_bit, high_bit + 1):
                bit_to_field[bit] = field_name

        # 打印位映射表
        print(f"位位置: {total_bits-1:2d} ... 0")
        print("-" * 80)

        # 按8位一组打印
        for start_bit in range(total_bits - 1, -1, -8):
            end_bit = max(start_bit - 7, 0)

            # 打印位号
            bit_line = ""
            field_line = ""

            for bit in range(start_bit, end_bit - 1, -1):
                bit_line += f"{bit:2d} "
                field_name = bit_to_field.get(bit, "--")
                field_line += f"{field_name:2s} "

            print(f"位: {bit_line}")
            print(f"字段: {field_line}")
            print()

        # 打印字段详细信息
        print("字段详细信息:")
        print("-" * 80)
        for field_name, field in self.mapping_fields.items():
            high_bit, low_bit = field.position
            ranges_str = ', '.join(f"[{r[0]:5d}, {r[1]:5d}]" for r in field.valid_ranges)
            print(f"{field_name:4s}: 位[{high_bit:2d}:{low_bit:2d}] "
                  f"({field.bits:2d}位) 有效范围{ranges_str} "
                  f"- {field.description}")

        # 如果提供了地址，显示具体映射
        if address is not None:
            print("\n" + "=" * 80)
            print(f"地址 0x{address:08x} 的映射结果:")
            print("=" * 80)
            try:
                mapping = self.addr_to_mapping(address)
                inner_addr = self.addr_to_inner_addr(address)

                print(f"原始地址: 0x{address:08x}")
                print(f"内部地址: 0x{inner_addr:08x} (二进制: {inner_addr:032b})")
                print()

                for field_name, value in mapping.items():
                    field = self.mapping_fields[field_name]
                    high_bit, low_bit = field.position
                    binary_val = f"{value:0{field.bits}b}"
                    print(f"{field_name:4s}: {value:5d} (0x{value:04x}) (二进制: {binary_val}) "
                          f"位[{high_bit}:{low_bit}]")

            except Exception as e:
                print(f"映射失败: {e}")

        print("=" * 80)

    def rand_addrmap(self, count: Optional[int] = None, fixed_mapping: Optional[Dict[str, int]] = None) -> List[Dict[str, Any]]:
        """
        随机生成一个合规的映射信息

        Args:
            count: 生成数量，如果为None则使用配置文件中的值
            fixed_mapping: 固定的映射配置，作为随机生成的基础

        Returns:
            随机生成的地址和映射信息列表
        """
        if count is None:
            count = self.config['random_generation'].get('count', 10)

        # 设置随机种子
        seed = self.config['random_generation'].get('seed')
        if seed is not None:
            random.seed(seed)

        valid_only = self.config['random_generation'].get('valid_only', True)
        results = []

        for _ in range(count):
            if valid_only:
                # 生成有效的映射值，但需要考虑对齐要求
                alignment = self.config['address_conversion'].get('alignment', 1)

                # 先生成一个基础的映射值，考虑固定配置
                mapping_values = {}
                for field_name, field in self.mapping_fields.items():
                    if fixed_mapping and field_name in fixed_mapping:
                        # 使用固定配置的值
                        fixed_value = fixed_mapping[field_name]
                        if field.is_valid_value(fixed_value):
                            mapping_values[field_name] = fixed_value
                        else:
                            ranges_str = ', '.join(f"[{r[0]}, {r[1]}]" for r in field.valid_ranges)
                            raise ValueError(f"固定配置中字段 {field_name} 的值 {fixed_value} 不在有效范围内: {ranges_str}")
                    else:
                        # 随机选择一个有效范围，然后在该范围内随机选择值
                        valid_range = random.choice(field.valid_ranges)
                        min_val, max_val = valid_range
                        mapping_values[field_name] = random.randint(min_val, max_val)

                # 如果有对齐要求，需要调整低位字段
                if alignment > 1:
                    # 找到覆盖对齐位的字段
                    align_bits = alignment.bit_length() - 1  # 对齐位数
                    for field_name, field in self.mapping_fields.items():
                        high_bit, low_bit = field.position
                        if low_bit < align_bits:
                            # 这个字段的低位需要被对齐约束
                            affected_bits = min(align_bits, high_bit + 1) - low_bit
                            if affected_bits > 0:
                                # 清除受影响的低位
                                mask = ((1 << affected_bits) - 1)
                                mapping_values[field_name] &= ~mask

                # 根据映射值构造地址
                inner_addr = 0
                for field_name, value in mapping_values.items():
                    field = self.mapping_fields[field_name]
                    high_bit, low_bit = field.position
                    inner_addr |= (value << low_bit)

                # 转换为外部地址
                base_addr = self.address_space.get('base_address', 0)
                offset = self.config['address_conversion'].get('inner_addr_offset', 0)
                address = inner_addr + base_addr - offset

            else:
                # 完全随机生成地址
                max_addr = self.address_space.get('size', 0xFFFFFFFF)
                base_addr = self.address_space.get('base_address', 0)
                address = random.randint(base_addr, base_addr + max_addr - 1)

                try:
                    mapping_values = self.addr_to_mapping(address)
                except ValueError:
                    # 如果地址无效，跳过
                    continue

            # 构造结果
            result = {
                'address': address,
                'address_hex': f"0x{address:08x}",
                'inner_address': self.addr_to_inner_addr(address),
                'mapping': mapping_values
            }

            results.append(result)

        return results

    def mapping_to_addr(self, mapping: Dict[str, int]) -> int:
        """
        根据映射字段值构造地址

        Args:
            mapping: 映射字段字典 {字段名: 值}

        Returns:
            构造的地址
        """
        # 验证映射字段
        for field_name, value in mapping.items():
            if field_name not in self.mapping_fields:
                raise ValueError(f"未知的映射字段: {field_name}")

            field = self.mapping_fields[field_name]
            if not field.is_valid_value(value):
                ranges_str = ', '.join(f"[{r[0]}, {r[1]}]" for r in field.valid_ranges)
                raise ValueError(f"字段 {field_name} 的值 {value} 不在任何有效范围内: {ranges_str}")

        # 检查对齐约束并调整映射值
        alignment = self.config['address_conversion'].get('alignment', 1)
        adjusted_mapping = mapping.copy()

        if alignment > 1:
            align_bits = alignment.bit_length() - 1
            for field_name, value in adjusted_mapping.items():
                field = self.mapping_fields[field_name]
                high_bit, low_bit = field.position
                if low_bit < align_bits:
                    # 清除受对齐影响的低位
                    affected_bits = min(align_bits, high_bit + 1) - low_bit
                    if affected_bits > 0:
                        mask = ((1 << affected_bits) - 1)
                        adjusted_mapping[field_name] = value & ~mask

        # 构造内部地址
        inner_addr = 0
        for field_name, value in adjusted_mapping.items():
            field = self.mapping_fields[field_name]
            high_bit, low_bit = field.position
            inner_addr |= (value << low_bit)

        # 转换为外部地址
        base_addr = self.address_space.get('base_address', 0)
        offset = self.config['address_conversion'].get('inner_addr_offset', 0)
        address = inner_addr + base_addr - offset

        return address
