#!/usr/bin/env python3
"""
地址映射系统演示程序
展示AddrMap类的各种功能
"""

from addrmap import AddrMap
import json


def demo_check_addrmap():
    """演示映射表检查功能"""
    print("\n" + "="*60)
    print("1. 演示映射表检查功能 (check_addrmap)")
    print("="*60)
    
    # 创建地址映射器
    addr_mapper = AddrMap("config.yaml")
    
    # 检查映射表
    check_result = addr_mapper.check_addrmap()
    
    print("映射表检查结果:")
    print(f"  有效性: {'通过' if check_result['valid'] else '失败'}")
    print(f"  位重叠检查: {'通过' if check_result['overlap_check'] else '失败'}")
    
    if check_result['errors']:
        print("  错误信息:")
        for error in check_result['errors']:
            print(f"    - {error}")
    
    if check_result['warnings']:
        print("  警告信息:")
        for warning in check_result['warnings']:
            print(f"    - {warning}")
    
    if 'coverage' in check_result:
        coverage = check_result['coverage']
        print(f"  地址覆盖率: {coverage['coverage_ratio']:.2%}")
        if not coverage['complete']:
            print(f"  未覆盖位: {coverage['uncovered_bits']}")


def demo_addr_to_mapping():
    """演示地址到映射转换功能"""
    print("\n" + "="*60)
    print("2. 演示地址到映射转换功能 (addr_to_mapping)")
    print("="*60)
    
    addr_mapper = AddrMap("config.yaml")
    
    # 测试几个地址
    test_addresses = [
        0x00000000,  # 全零地址
        0x12345678,  # 随机地址
        0xFFFFFFFF,  # 全一地址
        0xAAAAAAAA,  # 交替位模式
    ]
    
    for addr in test_addresses:
        print(f"\n地址 0x{addr:08x} 的映射结果:")
        try:
            # 转换为内部地址
            inner_addr = addr_mapper.addr_to_inner_addr(addr)
            print(f"  内部地址: 0x{inner_addr:08x}")
            
            # 获取映射
            mapping = addr_mapper.addr_to_mapping(addr)
            print("  映射字段:")
            for field_name, value in mapping.items():
                print(f"    {field_name}: {value} (0x{value:x})")
                
        except Exception as e:
            print(f"  转换失败: {e}")


def demo_print_addrmap():
    """演示映射表打印功能"""
    print("\n" + "="*60)
    print("3. 演示映射表打印功能 (print_addrmap)")
    print("="*60)
    
    addr_mapper = AddrMap("config.yaml")
    
    # 打印基本映射表
    print("基本映射表:")
    addr_mapper.print_addrmap()
    
    # 打印特定地址的映射
    test_addr = 0x12345678
    print(f"\n特定地址 0x{test_addr:08x} 的详细映射:")
    addr_mapper.print_addrmap(test_addr)


def demo_rand_addrmap():
    """演示随机地址生成功能"""
    print("\n" + "="*60)
    print("4. 演示随机地址生成功能 (rand_addrmap)")
    print("="*60)
    
    addr_mapper = AddrMap("config.yaml")
    
    # 生成随机地址映射
    random_mappings = addr_mapper.rand_addrmap(5)  # 生成5个随机映射
    
    print("随机生成的地址映射:")
    for i, result in enumerate(random_mappings, 1):
        print(f"\n随机映射 {i}:")
        print(f"  地址: {result['address_hex']}")
        print(f"  内部地址: 0x{result['inner_address']:08x}")
        print("  映射字段:")
        for field_name, value in result['mapping'].items():
            print(f"    {field_name}: {value}")


def demo_mapping_to_addr():
    """演示从映射字段构造地址功能"""
    print("\n" + "="*60)
    print("5. 演示从映射字段构造地址功能 (mapping_to_addr)")
    print("="*60)
    
    addr_mapper = AddrMap("config.yaml")
    
    # 定义一些测试映射
    test_mappings = [
        {'cs': 0, 'bg': 0, 'ba': 0, 'row': 0, 'col': 0},      # 全零映射
        {'cs': 1, 'bg': 2, 'ba': 3, 'row': 1000, 'col': 500}, # 随机映射
        {'cs': 3, 'bg': 3, 'ba': 3, 'row': 65535, 'col': 1023}, # 最大值映射
    ]
    
    for i, mapping in enumerate(test_mappings, 1):
        print(f"\n测试映射 {i}: {mapping}")
        try:
            # 构造地址
            address = addr_mapper.mapping_to_addr(mapping)
            print(f"  构造的地址: 0x{address:08x}")
            
            # 验证：将地址转换回映射
            reverse_mapping = addr_mapper.addr_to_mapping(address)
            print(f"  验证映射: {reverse_mapping}")
            
            # 检查是否一致
            if mapping == reverse_mapping:
                print("  ✓ 映射一致性验证通过")
            else:
                print("  ✗ 映射一致性验证失败")
                
        except Exception as e:
            print(f"  构造失败: {e}")


def demo_comprehensive_test():
    """综合测试演示"""
    print("\n" + "="*60)
    print("6. 综合测试演示")
    print("="*60)
    
    addr_mapper = AddrMap("config.yaml")
    
    print("执行完整的系统测试...")
    
    # 1. 检查映射表
    check_result = addr_mapper.check_addrmap()
    print(f"映射表检查: {'通过' if check_result['valid'] else '失败'}")
    
    # 2. 测试地址转换的双向一致性
    print("\n测试地址转换双向一致性:")
    test_count = 0
    success_count = 0
    
    # 生成一些随机映射进行测试
    random_results = addr_mapper.rand_addrmap(10)
    
    for result in random_results:
        test_count += 1
        original_mapping = result['mapping']
        address = result['address']
        
        try:
            # 地址 -> 映射 -> 地址
            converted_mapping = addr_mapper.addr_to_mapping(address)
            reconstructed_addr = addr_mapper.mapping_to_addr(converted_mapping)
            
            if address == reconstructed_addr and original_mapping == converted_mapping:
                success_count += 1
            else:
                print(f"  不一致: 原始地址=0x{address:08x}, 重构地址=0x{reconstructed_addr:08x}")
                
        except Exception as e:
            print(f"  测试失败: {e}")
    
    print(f"双向一致性测试: {success_count}/{test_count} 通过 ({success_count/test_count:.1%})")


def main():
    """主函数"""
    print("地址映射系统演示程序")
    print("="*60)
    
    try:
        # 依次执行各个演示
        demo_check_addrmap()
        demo_addr_to_mapping()
        demo_print_addrmap()
        demo_rand_addrmap()
        demo_mapping_to_addr()
        demo_comprehensive_test()
        
        print("\n" + "="*60)
        print("演示程序执行完成！")
        print("="*60)
        
    except Exception as e:
        print(f"演示程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
