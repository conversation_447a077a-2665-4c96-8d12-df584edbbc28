#!/usr/bin/env python3
"""
预留地址映射系统单元测试
"""

import unittest
import tempfile
import os
import yaml
from reserved_addrmap import ReservedAddrMapGenerator, SubField, ConfigSpace


class TestSubField(unittest.TestCase):
    """SubField类的单元测试"""
    
    def test_subfield_creation(self):
        """测试SubField创建"""
        field = SubField(
            name="test_field",
            start_bit=0,
            end_bit=7,
            default_offset=0,
            valid_ranges=[[0, 255]],
            disable_enable=True,
            disable_value=255,
            mapping_field="TEST",
            base_value=100
        )
        
        self.assertEqual(field.name, "test_field")
        self.assertEqual(field.bits, 8)
        self.assertEqual(field.max_value, 255)
        self.assertTrue(field.is_valid_value(128))
        self.assertFalse(field.is_valid_value(256))
        self.assertEqual(field.get_actual_value(50), 150)  # 50 + 100
        self.assertEqual(field.get_actual_value(255), 255)  # 失能值
    
    def test_multiple_valid_ranges(self):
        """测试多个有效范围"""
        field = SubField(
            name="multi_range",
            start_bit=0,
            end_bit=7,
            default_offset=0,
            valid_ranges=[[0, 10], [20, 30], [50, 60]],
            disable_enable=False,
            disable_value=None,
            mapping_field="MULTI",
            base_value=0
        )
        
        self.assertTrue(field.is_valid_value(5))   # 在第一个范围
        self.assertTrue(field.is_valid_value(25))  # 在第二个范围
        self.assertTrue(field.is_valid_value(55))  # 在第三个范围
        self.assertFalse(field.is_valid_value(15)) # 不在任何范围
        self.assertFalse(field.is_valid_value(35)) # 不在任何范围


class TestReservedAddrMapGenerator(unittest.TestCase):
    """ReservedAddrMapGenerator类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时配置文件
        self.test_config = {
            'config_spaces': {
                'test_space': {
                    'bit_width': 16,
                    'sub_fields': {
                        'field1': {
                            'start_bit': 8,
                            'end_bit': 15,
                            'default_offset': 0,
                            'valid_ranges': [[0, 255]],
                            'disable_enable': True,
                            'disable_value': 255,
                            'mapping_field': 'F1',
                            'base_value': 0
                        },
                        'field2': {
                            'start_bit': 0,
                            'end_bit': 7,
                            'default_offset': 0,
                            'valid_ranges': [[0, 127], [200, 255]],
                            'disable_enable': False,
                            'mapping_field': 'F2',
                            'base_value': 100
                        }
                    }
                }
            },
            'global_config': {
                'random_generation': {
                    'seed': 42,
                    'disable_probability': 0.1
                }
            }
        }
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config, default_flow_style=False)
        self.temp_config.close()
        
        # 创建生成器实例
        self.generator = ReservedAddrMapGenerator(self.temp_config.name)
    
    def tearDown(self):
        """测试后的清理"""
        # 删除临时配置文件
        os.unlink(self.temp_config.name)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.generator.config)
        self.assertIsNotNone(self.generator.config_spaces)
        self.assertEqual(len(self.generator.config_spaces), 1)
        
        # 检查配置空间解析
        self.assertIn('test_space', self.generator.config_spaces)
        space = self.generator.config_spaces['test_space']
        self.assertEqual(space.bit_width, 16)
        self.assertEqual(len(space.sub_fields), 2)
    
    def test_generate_random_config(self):
        """测试随机配置生成"""
        config = self.generator.generate_random_config()
        
        self.assertIn('test_space', config)
        space_config = config['test_space']
        
        self.assertIn('field1', space_config)
        self.assertIn('field2', space_config)
        
        # 检查值的有效性
        field1_value = space_config['field1']
        field2_value = space_config['field2']
        
        self.assertTrue(0 <= field1_value <= 255)
        self.assertTrue((0 <= field2_value <= 127) or (200 <= field2_value <= 255))
    
    def test_generate_random_config_with_fixed(self):
        """测试带固定配置的随机生成"""
        fixed_configs = {
            'test_space': {
                'field1': 100
            }
        }
        
        config = self.generator.generate_random_config(fixed_configs)
        
        # 检查固定配置是否被应用
        self.assertEqual(config['test_space']['field1'], 100)
        
        # 检查其他字段是否随机生成
        self.assertIn('field2', config['test_space'])
    
    def test_generate_random_config_invalid_fixed(self):
        """测试无效固定配置"""
        fixed_configs = {
            'test_space': {
                'field2': 150  # 不在有效范围内
            }
        }
        
        with self.assertRaises(ValueError):
            self.generator.generate_random_config(fixed_configs)
    
    def test_generate_mapping_table(self):
        """测试映射表生成"""
        config = {
            'test_space': {
                'field1': 50,
                'field2': 25
            }
        }
        
        mapping_table = self.generator.generate_mapping_table(config)
        
        self.assertIn('F1', mapping_table)
        self.assertIn('F2', mapping_table)
        
        # field1: 实际值 = 50 + 0 = 50
        # field2: 实际值 = 25 + 100 = 125
        self.assertEqual(mapping_table['F1'], 50)
        self.assertEqual(mapping_table['F2'], 125)
    
    def test_print_functions(self):
        """测试打印功能（基本测试，确保不抛异常）"""
        try:
            # 测试字段描述打印
            self.generator.print_field_description()
            
            # 测试具体配置打印
            config = self.generator.generate_random_config()
            self.generator.print_specific_config(config)
            
        except Exception as e:
            self.fail(f"打印功能抛出了异常: {e}")
    
    def test_disable_functionality(self):
        """测试失能功能"""
        config = {
            'test_space': {
                'field1': 255,  # 失能值
                'field2': 50
            }
        }
        
        mapping_table = self.generator.generate_mapping_table(config)
        
        # field1 处于失能状态，实际值应该是失能值本身
        self.assertEqual(mapping_table['F1'], 255)
        # field2 正常，实际值 = 50 + 100 = 150
        self.assertEqual(mapping_table['F2'], 150)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_addrmap_integration(self):
        """测试与AddrMap的集成"""
        # 这个测试需要确保两个系统能够协同工作
        try:
            from addrmap import AddrMap
            
            # 创建两个系统的实例
            addr_mapper = AddrMap("config.yaml")
            
            # 测试多范围有效性
            test_mapping = {'cs': 0, 'bg': 1, 'ba': 2, 'row': 100, 'col': 48}
            
            # 测试带固定配置的随机生成
            random_results = addr_mapper.rand_addrmap(3, test_mapping)
            
            self.assertEqual(len(random_results), 3)
            
            # 验证固定配置是否被保持
            for result in random_results:
                mapping = result['mapping']
                for field, value in test_mapping.items():
                    if field in mapping:
                        self.assertEqual(mapping[field], value)
            
        except ImportError:
            self.skipTest("AddrMap模块不可用")
        except Exception as e:
            self.fail(f"集成测试失败: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
