# 地址映射配置文件
# 定义地址空间和映射字段的配置信息

# 地址空间配置
address_space:
  # 总地址位宽
  total_bits: 32
  # 地址基址（可选）
  base_address: 0x00000000
  # 地址大小（字节数）
  size: 0x100000000  # 4GB

# 映射字段配置
# 每个字段包含：位宽、起始位置、有效范围
mapping_fields:
  cs:  # Chip Select
    bits: 2          # 位宽
    position: [31, 30]  # 位位置 [高位, 低位]
    valid_range: [0, 3]  # 有效值范围
    description: "芯片选择信号"
    
  bg:  # Bank Group
    bits: 2
    position: [29, 28]
    valid_range: [0, 3]
    description: "存储体组"
    
  ba:  # Bank Address
    bits: 2
    position: [27, 26]
    valid_range: [0, 3]
    description: "存储体地址"
    
  row: # Row Address
    bits: 16
    position: [25, 10]
    valid_range: [0, 65535]
    description: "行地址"
    
  col: # Column Address
    bits: 10
    position: [9, 0]
    valid_range: [0, 1023]
    description: "列地址"

# 地址转换配置
address_conversion:
  # 内部地址偏移
  inner_addr_offset: 0
  # 地址对齐要求（字节）
  alignment: 4
  # 是否启用地址检查
  enable_check: true

# 映射表验证配置
validation:
  # 是否检查位重叠
  check_overlap: true
  # 是否检查覆盖完整性
  check_coverage: true
  # 是否检查有效范围
  check_range: true

# 随机生成配置
random_generation:
  # 随机种子（可选）
  seed: null
  # 生成数量
  count: 10
  # 是否只生成有效地址
  valid_only: true
