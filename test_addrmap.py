#!/usr/bin/env python3
"""
地址映射系统单元测试
"""

import unittest
import tempfile
import os
import yaml
from addrmap import AddrMap, MappingField


class TestAddrMap(unittest.TestCase):
    """AddrMap类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时配置文件
        self.test_config = {
            'address_space': {
                'total_bits': 32,
                'base_address': 0x00000000,
                'size': 0x100000000
            },
            'mapping_fields': {
                'cs': {
                    'bits': 2,
                    'position': [31, 30],
                    'valid_ranges': [[0, 3]],
                    'description': '芯片选择信号'
                },
                'bg': {
                    'bits': 2,
                    'position': [29, 28],
                    'valid_ranges': [[0, 3]],
                    'description': '存储体组'
                },
                'ba': {
                    'bits': 2,
                    'position': [27, 26],
                    'valid_ranges': [[0, 3]],
                    'description': '存储体地址'
                },
                'row': {
                    'bits': 16,
                    'position': [25, 10],
                    'valid_ranges': [[0, 65535]],
                    'description': '行地址'
                },
                'col': {
                    'bits': 10,
                    'position': [9, 0],
                    'valid_ranges': [[0, 1023]],
                    'description': '列地址'
                }
            },
            'address_conversion': {
                'inner_addr_offset': 0,
                'alignment': 4,
                'enable_check': True
            },
            'validation': {
                'check_overlap': True,
                'check_coverage': True,
                'check_range': True
            },
            'random_generation': {
                'seed': 42,
                'count': 10,
                'valid_only': True
            }
        }
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config, default_flow_style=False)
        self.temp_config.close()
        
        # 创建AddrMap实例
        self.addr_mapper = AddrMap(self.temp_config.name)
    
    def tearDown(self):
        """测试后的清理"""
        # 删除临时配置文件
        os.unlink(self.temp_config.name)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.addr_mapper.config)
        self.assertIsNotNone(self.addr_mapper.mapping_fields)
        self.assertEqual(len(self.addr_mapper.mapping_fields), 5)
        
        # 检查字段解析
        self.assertIn('cs', self.addr_mapper.mapping_fields)
        cs_field = self.addr_mapper.mapping_fields['cs']
        self.assertEqual(cs_field.bits, 2)
        self.assertEqual(cs_field.position, [31, 30])
        self.assertEqual(cs_field.valid_ranges, [[0, 3]])
        self.assertEqual(cs_field.valid_range, [0, 3])  # 兼容性属性
    
    def test_check_addrmap_valid(self):
        """测试有效映射表的检查"""
        result = self.addr_mapper.check_addrmap()
        
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['errors']), 0)
        self.assertTrue(result['overlap_check'])
        self.assertTrue(result['coverage']['complete'])
    
    def test_check_addrmap_overlap(self):
        """测试位重叠检查"""
        # 创建有重叠的配置
        overlap_config = self.test_config.copy()
        overlap_config['mapping_fields']['test_overlap'] = {
            'bits': 2,
            'position': [31, 30],  # 与cs字段重叠
            'valid_range': [0, 3],
            'description': '测试重叠字段'
        }
        
        # 创建临时文件
        temp_overlap = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(overlap_config, temp_overlap, default_flow_style=False)
        temp_overlap.close()
        
        try:
            overlap_mapper = AddrMap(temp_overlap.name)
            result = overlap_mapper.check_addrmap()
            
            self.assertFalse(result['valid'])
            self.assertFalse(result['overlap_check'])
            self.assertGreater(len(result['errors']), 0)
            
        finally:
            os.unlink(temp_overlap.name)
    
    def test_addr_to_inner_addr(self):
        """测试地址到内部地址转换"""
        # 测试基本转换
        inner_addr = self.addr_mapper.addr_to_inner_addr(0x12345678)
        self.assertEqual(inner_addr, 0x12345678)
        
        # 测试对齐检查
        with self.assertRaises(ValueError):
            self.addr_mapper.addr_to_inner_addr(0x12345679)  # 不满足4字节对齐
    
    def test_addr_to_mapping(self):
        """测试地址到映射转换"""
        # 测试全零地址
        mapping = self.addr_mapper.addr_to_mapping(0x00000000)
        expected = {'cs': 0, 'bg': 0, 'ba': 0, 'row': 0, 'col': 0}
        self.assertEqual(mapping, expected)
        
        # 测试特定地址
        # 地址 0xC0000000 = 11000000000000000000000000000000 (二进制)
        # cs[31:30] = 11 = 3, bg[29:28] = 00 = 0, ba[27:26] = 00 = 0
        # row[25:10] = 0000000000000000 = 0, col[9:0] = 0000000000 = 0
        mapping = self.addr_mapper.addr_to_mapping(0xC0000000)
        expected = {'cs': 3, 'bg': 0, 'ba': 0, 'row': 0, 'col': 0}
        self.assertEqual(mapping, expected)
    
    def test_mapping_to_addr(self):
        """测试映射到地址转换"""
        # 测试基本转换，使用对齐友好的映射值
        mapping = {'cs': 3, 'bg': 2, 'ba': 1, 'row': 100, 'col': 48}  # col=48是4字节对齐的
        address = self.addr_mapper.mapping_to_addr(mapping)

        # 验证转换结果
        reverse_mapping = self.addr_mapper.addr_to_mapping(address)
        self.assertEqual(mapping, reverse_mapping)
    
    def test_bidirectional_conversion(self):
        """测试双向转换一致性"""
        test_addresses = [0x00000000, 0x12345678, 0xAAAAAAAA]
        
        for addr in test_addresses:
            # 确保地址满足对齐要求
            aligned_addr = addr & ~3
            
            try:
                # 地址 -> 映射 -> 地址
                mapping = self.addr_mapper.addr_to_mapping(aligned_addr)
                reconstructed_addr = self.addr_mapper.mapping_to_addr(mapping)
                
                self.assertEqual(aligned_addr, reconstructed_addr)
                
            except ValueError:
                # 某些地址可能超出有效范围，这是正常的
                pass
    
    def test_rand_addrmap(self):
        """测试随机地址生成"""
        random_results = self.addr_mapper.rand_addrmap(5)
        
        self.assertEqual(len(random_results), 5)
        
        for result in random_results:
            self.assertIn('address', result)
            self.assertIn('address_hex', result)
            self.assertIn('inner_address', result)
            self.assertIn('mapping', result)
            
            # 验证映射的一致性
            mapping = result['mapping']
            address = result['address']
            
            try:
                reverse_mapping = self.addr_mapper.addr_to_mapping(address)
                self.assertEqual(mapping, reverse_mapping)
            except ValueError:
                # 某些随机地址可能无效，这是正常的
                pass
    
    def test_invalid_mapping_values(self):
        """测试无效映射值"""
        # 测试超出范围的值
        invalid_mapping = {'cs': 5, 'bg': 0, 'ba': 0, 'row': 0, 'col': 0}  # cs最大值为3
        
        with self.assertRaises(ValueError):
            self.addr_mapper.mapping_to_addr(invalid_mapping)
    
    def test_unknown_field(self):
        """测试未知字段"""
        invalid_mapping = {'unknown_field': 1, 'cs': 0, 'bg': 0, 'ba': 0, 'row': 0, 'col': 0}
        
        with self.assertRaises(ValueError):
            self.addr_mapper.mapping_to_addr(invalid_mapping)
    
    def test_print_addrmap(self):
        """测试打印功能（基本测试，主要确保不抛异常）"""
        try:
            # 测试基本打印
            self.addr_mapper.print_addrmap()

            # 测试带地址的打印
            self.addr_mapper.print_addrmap(0x12345678)

        except Exception as e:
            self.fail(f"print_addrmap 抛出了异常: {e}")

    def test_multi_range_validation(self):
        """测试多范围有效性验证"""
        # 创建有多个范围的配置
        multi_range_config = self.test_config.copy()
        multi_range_config['mapping_fields']['cs']['valid_ranges'] = [[0, 1], [3, 3]]

        # 创建临时文件
        temp_multi = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(multi_range_config, temp_multi, default_flow_style=False)
        temp_multi.close()

        try:
            multi_mapper = AddrMap(temp_multi.name)
            cs_field = multi_mapper.mapping_fields['cs']

            # 测试有效值
            self.assertTrue(cs_field.is_valid_value(0))  # 在第一个范围
            self.assertTrue(cs_field.is_valid_value(1))  # 在第一个范围
            self.assertTrue(cs_field.is_valid_value(3))  # 在第二个范围

            # 测试无效值
            self.assertFalse(cs_field.is_valid_value(2))  # 不在任何范围

        finally:
            os.unlink(temp_multi.name)

    def test_rand_addrmap_with_fixed_mapping(self):
        """测试带固定配置的随机生成"""
        fixed_mapping = {'cs': 1, 'bg': 2}

        random_results = self.addr_mapper.rand_addrmap(5, fixed_mapping)

        self.assertEqual(len(random_results), 5)

        # 验证固定配置是否被保持
        for result in random_results:
            mapping = result['mapping']
            self.assertEqual(mapping['cs'], 1)
            self.assertEqual(mapping['bg'], 2)
            # 其他字段应该是随机的
            self.assertIn('ba', mapping)
            self.assertIn('row', mapping)
            self.assertIn('col', mapping)


class TestMappingField(unittest.TestCase):
    """MappingField数据类的测试"""
    
    def test_mapping_field_creation(self):
        """测试MappingField创建"""
        field = MappingField(
            name="test",
            bits=4,
            position=[15, 12],
            valid_ranges=[[0, 15]],
            description="测试字段"
        )

        self.assertEqual(field.name, "test")
        self.assertEqual(field.bits, 4)
        self.assertEqual(field.position, [15, 12])
        self.assertEqual(field.valid_ranges, [[0, 15]])
        self.assertEqual(field.valid_range, [0, 15])  # 兼容性属性
        self.assertEqual(field.description, "测试字段")
        self.assertTrue(field.is_valid_value(10))
        self.assertFalse(field.is_valid_value(20))


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
