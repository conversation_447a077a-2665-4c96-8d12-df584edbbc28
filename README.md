# 地址映射系统 (Address Mapping System)

一个用Python实现的灵活地址映射系统，支持将内存地址映射到多个字段（如cs、bg、ba、row、col等）。

## 功能特性

- **地址转换**: 将外部地址转换为内部地址
- **字段映射**: 根据配置将地址位映射到不同字段
- **映射验证**: 检查映射表的完整性和有效性
- **随机生成**: 生成符合规则的随机地址映射
- **可视化**: 打印详细的地址映射表
- **双向转换**: 支持地址到映射和映射到地址的双向转换

## 文件结构

```
yenp-addrmap/
├── addrmap.py          # 核心地址映射类
├── config.yaml         # 配置文件
├── demo.py            # 演示程序
├── test_addrmap.py    # 单元测试
├── requirements.txt   # 依赖包
└── README.md         # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置文件说明

`config.yaml` 包含以下配置项：

### 地址空间配置
- `total_bits`: 总地址位宽
- `base_address`: 地址基址
- `size`: 地址空间大小

### 映射字段配置
每个字段包含：
- `bits`: 字段位宽
- `position`: 在地址中的位位置 [高位, 低位]
- `valid_range`: 有效值范围 [最小值, 最大值]
- `description`: 字段描述

### 示例配置
```yaml
mapping_fields:
  cs:  # Chip Select
    bits: 2
    position: [31, 30]
    valid_range: [0, 3]
    description: "芯片选择信号"
  
  row: # Row Address
    bits: 16
    position: [25, 10]
    valid_range: [0, 65535]
    description: "行地址"
```

## 核心类方法

### AddrMap 类

#### `check_addrmap()`
检查映射表是否完整，包括：
- 位重叠检查
- 地址覆盖完整性检查
- 有效范围检查

#### `addr_to_mapping(address)`
将地址转换为映射字段字典：
```python
mapping = addr_mapper.addr_to_mapping(0x12345678)
# 返回: {'cs': 0, 'bg': 1, 'ba': 2, 'row': 13398, 'col': 632}
```

#### `print_addrmap(address=None)`
打印地址映射表，可选择显示特定地址的映射结果

#### `rand_addrmap(count=None)`
随机生成符合规则的地址映射：
```python
random_mappings = addr_mapper.rand_addrmap(5)
```

#### `mapping_to_addr(mapping)`
根据映射字段构造地址：
```python
address = addr_mapper.mapping_to_addr({'cs': 1, 'bg': 2, 'ba': 0, 'row': 100, 'col': 50})
```

## 使用示例

### 基本使用
```python
from addrmap import AddrMap

# 创建地址映射器
addr_mapper = AddrMap("config.yaml")

# 检查映射表
result = addr_mapper.check_addrmap()
print(f"映射表有效: {result['valid']}")

# 地址转换
address = 0x12345678
mapping = addr_mapper.addr_to_mapping(address)
print(f"地址 0x{address:08x} 映射为: {mapping}")

# 反向转换
reconstructed_addr = addr_mapper.mapping_to_addr(mapping)
print(f"重构地址: 0x{reconstructed_addr:08x}")
```

### 运行演示程序
```bash
python demo.py
```

### 运行测试
```bash
python test_addrmap.py
```

## 演示程序功能

`demo.py` 包含以下演示：

1. **映射表检查演示**: 展示如何验证映射配置
2. **地址转换演示**: 演示地址到映射字段的转换
3. **映射表打印演示**: 展示可视化输出
4. **随机生成演示**: 演示随机地址映射生成
5. **反向转换演示**: 演示从映射字段构造地址
6. **综合测试演示**: 完整的系统测试

## 测试覆盖

单元测试覆盖以下功能：
- 初始化和配置加载
- 映射表有效性检查
- 地址转换功能
- 双向转换一致性
- 随机生成功能
- 错误处理

## 扩展性

系统设计具有良好的扩展性：
- 可以通过修改配置文件添加新的映射字段
- 支持不同的地址位宽和映射规则
- 可以自定义验证规则和转换逻辑

## 注意事项

1. 地址必须满足配置的对齐要求
2. 映射字段值必须在有效范围内
3. 映射字段的位位置不能重叠
4. 建议在使用前先运行 `check_addrmap()` 验证配置
