#!/usr/bin/env python3
"""
预留地址映射系统演示程序
展示ReservedAddrMapGenerator类的各种功能
"""

from reserved_addrmap import ReservedAddrMapGenerator
from addrmap import AddrMap
import json


def demo_print_field_description():
    """演示字段描述打印功能"""
    print("\n" + "="*80)
    print("1. 演示字段描述打印功能")
    print("="*80)
    
    # 创建预留地址映射生成器
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    # 打印字段描述
    generator.print_field_description()


def demo_random_config_generation():
    """演示随机配置生成功能"""
    print("\n" + "="*80)
    print("2. 演示随机配置生成功能")
    print("="*80)
    
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    print("生成完全随机的配置:")
    random_config = generator.generate_random_config()
    
    for space_name, space_config in random_config.items():
        print(f"\n{space_name}:")
        for field_name, value in space_config.items():
            print(f"  {field_name}: {value}")
    
    print("\n" + "-"*60)
    print("生成带固定配置基础的随机配置:")
    
    # 定义一些固定配置
    fixed_configs = {
        'memory_controller': {
            'chip_select': 1,  # 固定芯片选择为1
            'bank_group': 2    # 固定bank group为2
        },
        'cache_controller': {
            'cache_way': 4     # 固定为4路缓存
        }
    }
    
    print("固定配置:")
    for space_name, space_config in fixed_configs.items():
        print(f"  {space_name}: {space_config}")
    
    random_config_with_fixed = generator.generate_random_config(fixed_configs)
    
    print("\n生成的配置（包含固定部分）:")
    for space_name, space_config in random_config_with_fixed.items():
        print(f"\n{space_name}:")
        for field_name, value in space_config.items():
            fixed_mark = " (固定)" if space_name in fixed_configs and field_name in fixed_configs[space_name] else ""
            print(f"  {field_name}: {value}{fixed_mark}")


def demo_specific_config_printing():
    """演示具体配置打印功能"""
    print("\n" + "="*80)
    print("3. 演示具体配置打印功能")
    print("="*80)
    
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    # 生成一个随机配置
    config = generator.generate_random_config()
    
    print("打印具体配置信息:")
    generator.print_specific_config(config)


def demo_mapping_table_generation():
    """演示映射表生成功能"""
    print("\n" + "="*80)
    print("4. 演示映射表生成功能")
    print("="*80)
    
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    # 创建一个示例配置
    example_config = {
        'memory_controller': {
            'chip_select': 1,
            'bank_group': 2,
            'bank_addr': 1,
            'row_addr_high': 100,
            'col_addr': 512
        },
        'cache_controller': {
            'cache_way': 4,
            'cache_set': 32,
            'cache_line': 16
        },
        'peripheral_controller': {
            'device_id': 5,
            'channel_select': 3,
            'buffer_size': 1024,
            'base_address': 4096
        }
    }
    
    print("示例配置:")
    for space_name, space_config in example_config.items():
        print(f"  {space_name}: {space_config}")
    
    # 生成映射表
    mapping_table = generator.generate_mapping_table(example_config)
    
    print("\n生成的映射表:")
    for mapping_field, actual_value in mapping_table.items():
        print(f"  {mapping_field}: {actual_value}")


def demo_integration_with_addrmap():
    """演示与AddrMap类的集成"""
    print("\n" + "="*80)
    print("5. 演示与AddrMap类的集成")
    print("="*80)
    
    # 创建预留地址映射生成器
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    # 生成配置和映射表
    config = generator.generate_random_config()
    mapping_table = generator.generate_mapping_table(config)
    
    print("生成的映射表:")
    for field, value in mapping_table.items():
        print(f"  {field}: {value}")
    
    # 尝试与原始AddrMap系统集成
    print("\n尝试与原始地址映射系统集成:")
    try:
        addr_mapper = AddrMap("config.yaml")
        
        # 使用固定映射进行随机地址生成
        print("使用预留映射作为固定配置基础:")
        
        # 提取与原始系统兼容的字段
        compatible_mapping = {}
        field_mapping = {
            'CS0': 'cs',
            'BG0': 'bg', 
            'BA0': 'ba',
            'ROW0': 'row',
            'COL0': 'col'
        }
        
        for reserved_field, original_field in field_mapping.items():
            if reserved_field in mapping_table:
                # 确保值在原始系统的有效范围内
                value = mapping_table[reserved_field]
                if original_field in addr_mapper.mapping_fields:
                    field = addr_mapper.mapping_fields[original_field]
                    if field.is_valid_value(value):
                        compatible_mapping[original_field] = value
                    else:
                        # 使用第一个有效范围的最小值
                        compatible_mapping[original_field] = field.valid_ranges[0][0]
        
        print(f"兼容的映射: {compatible_mapping}")
        
        # 使用兼容映射生成随机地址
        if compatible_mapping:
            random_results = addr_mapper.rand_addrmap(3, compatible_mapping)
            print("\n基于预留映射的随机地址生成:")
            for i, result in enumerate(random_results, 1):
                print(f"  随机地址 {i}: {result['address_hex']}")
                print(f"    映射: {result['mapping']}")
        
    except Exception as e:
        print(f"集成失败: {e}")


def demo_comprehensive_workflow():
    """演示完整工作流程"""
    print("\n" + "="*80)
    print("6. 演示完整工作流程")
    print("="*80)
    
    generator = ReservedAddrMapGenerator("reserved_config.yaml")
    
    print("步骤1: 打印配置空间描述")
    print("-" * 40)
    generator.print_field_description()
    
    print("\n步骤2: 生成随机配置")
    print("-" * 40)
    config = generator.generate_random_config()
    
    print("\n步骤3: 打印具体配置")
    print("-" * 40)
    generator.print_specific_config(config)
    
    print("\n步骤4: 生成映射表")
    print("-" * 40)
    mapping_table = generator.generate_mapping_table(config)
    print("最终映射表:")
    for field, value in mapping_table.items():
        print(f"  {field}: {value}")
    
    print("\n步骤5: 验证配置有效性")
    print("-" * 40)
    total_fields = sum(len(space.sub_fields) for space in generator.config_spaces.values())
    configured_fields = sum(len(space_config) for space_config in config.values())
    print(f"总字段数: {total_fields}")
    print(f"已配置字段数: {configured_fields}")
    print(f"配置完整性: {configured_fields/total_fields:.1%}")


def main():
    """主函数"""
    print("预留地址映射系统演示程序")
    print("="*80)
    
    try:
        # 依次执行各个演示
        demo_print_field_description()
        demo_random_config_generation()
        demo_specific_config_printing()
        demo_mapping_table_generation()
        demo_integration_with_addrmap()
        demo_comprehensive_workflow()
        
        print("\n" + "="*80)
        print("预留地址映射演示程序执行完成！")
        print("="*80)
        
    except Exception as e:
        print(f"演示程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
