# 预留地址映射配置文件
# 定义多个配置空间和子字段的配置信息

config_spaces:
  # 配置空间1: 内存控制器配置
  memory_controller:
    bit_width: 32  # 32位配置空间
    sub_fields:
      chip_select:
        start_bit: 30
        end_bit: 31
        default_offset: 0
        valid_ranges: [[0, 1], [3, 3]]  # 支持0-1和3
        disable_enable: true
        disable_value: 3
        mapping_field: "CS0"
        base_value: 0
        
      bank_group:
        start_bit: 28
        end_bit: 29
        default_offset: 0
        valid_ranges: [[0, 3]]
        disable_enable: false
        mapping_field: "BG0"
        base_value: 0
        
      bank_addr:
        start_bit: 26
        end_bit: 27
        default_offset: 0
        valid_ranges: [[0, 3]]
        disable_enable: false
        mapping_field: "BA0"
        base_value: 0
        
      row_addr_high:
        start_bit: 16
        end_bit: 25
        default_offset: 0
        valid_ranges: [[0, 255], [512, 767]]  # 两个不连续范围
        disable_enable: true
        disable_value: 1023
        mapping_field: "ROW0"
        base_value: 1024  # 基值1024
        
      col_addr:
        start_bit: 0
        end_bit: 15
        default_offset: 4
        valid_ranges: [[0, 1023], [2048, 4095]]
        disable_enable: false
        mapping_field: "COL0"
        base_value: 0

  # 配置空间2: 缓存控制器配置  
  cache_controller:
    bit_width: 16  # 16位配置空间
    sub_fields:
      cache_way:
        start_bit: 12
        end_bit: 15
        default_offset: 0
        valid_ranges: [[1, 8]]  # 1-8路
        disable_enable: true
        disable_value: 0
        mapping_field: "WAY1"
        base_value: 0
        
      cache_set:
        start_bit: 6
        end_bit: 11
        default_offset: 0
        valid_ranges: [[0, 63]]
        disable_enable: false
        mapping_field: "SET1"
        base_value: 64  # 基值64
        
      cache_line:
        start_bit: 0
        end_bit: 5
        default_offset: 0
        valid_ranges: [[0, 31], [48, 63]]  # 两个范围
        disable_enable: true
        disable_value: 63
        mapping_field: "LINE1"
        base_value: 0

  # 配置空间3: 外设控制器配置
  peripheral_controller:
    bit_width: 64  # 64位配置空间
    sub_fields:
      device_id:
        start_bit: 56
        end_bit: 63
        default_offset: 0
        valid_ranges: [[1, 15], [32, 47]]  # 设备ID范围
        disable_enable: false
        mapping_field: "DEV_ID"
        base_value: 0
        
      channel_select:
        start_bit: 48
        end_bit: 55
        default_offset: 0
        valid_ranges: [[0, 7], [16, 23]]
        disable_enable: true
        disable_value: 255
        mapping_field: "CHAN"
        base_value: 0
        
      buffer_size:
        start_bit: 32
        end_bit: 47
        default_offset: 0
        valid_ranges: [[256, 4095], [8192, 16383]]  # 缓冲区大小
        disable_enable: false
        mapping_field: "BUF_SIZE"
        base_value: 0
        
      base_address:
        start_bit: 0
        end_bit: 31
        default_offset: 0
        valid_ranges: [[0, 1048575]]  # 1MB范围
        disable_enable: false
        mapping_field: "BASE_ADDR"
        base_value: 0x10000000  # 基地址

# 全局配置
global_config:
  # 随机生成配置
  random_generation:
    seed: 12345
    disable_probability: 0.1  # 失能字段的概率
    
  # 验证配置
  validation:
    check_ranges: true
    check_conflicts: true
